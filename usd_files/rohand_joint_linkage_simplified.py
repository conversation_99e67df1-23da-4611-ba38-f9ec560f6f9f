from omni.kit.scripting import BehaviorScript
from pxr import Sdf
import math
import omni.usd
import traceback
import asyncio


##
# Hand Joint Linkage - Simplified Version
##

MAX_ANGLES_URDF = 4
MAX_ANGLES_URDF_THUMB = 3
NUM_FINGERS = 5
EXTRA_MOTORS = 1

THUMB_ID = 0
INDEX_FINGER_ID = 1
MIDDLE_FINGER_ID = 2
RING_FINGER_ID = 3
LITTLE_FINGER_ID = 4
THUMB_ROOT_ID = 5

JOINTS_NAME = [
    ['righthand_th_proximal_link', 'righthand_th_slider_link', 'righthand_th_connecting_link', 'righthand_th_distal_link'],
    ['righthand_if_slider_link', 'righthand_if_slider_abpart_link', 'righthand_if_proximal_link', 'righthand_if_distal_link', 'righthand_if_connecting_link'],
    ['righthand_mf_slider_link', 'righthand_mf_slider_abpart_link', 'righthand_mf_proximal_link', 'righthand_mf_distal_link', 'righthand_mf_connecting_link'],
    ['righthand_rf_slider_link', 'righthand_rf_slider_abpart_link', 'righthand_rf_proximal_link', 'righthand_rf_distal_link', 'righthand_rf_connecting_link'],
    ['righthand_lf_slider_link', 'righthand_lf_slider_abpart_link', 'righthand_lf_proximal_link', 'righthand_lf_distal_link', 'righthand_lf_connecting_link'],
    ['righthand_th_root_link']
]

PI = 3.141592653589793
HALF_PI = 1.570796326794896
ONE_HALF_PI = 4.71238898038469

# 辅助函数
def METER_TO_MILLIMETER(value):
    return value * 1000

def CLAMP(value, min_value, max_value):
    return max(min(value, max_value), min_value)

def ANGLE_COS(L_a, L_b, L_c):
    return math.acos((L_b * L_b + L_c * L_c - L_a * L_a) / (2 * L_b * L_c))

def ANGLE_SIN(angle_a, L_a, L_b):
    return math.asin((L_b * math.sin(angle_a)) / L_a)

def LENGTH_COS(angle, L_b, L_c):
    return math.sqrt(L_b * L_b + L_c * L_c - 2 * L_b * L_c * math.cos(angle))

def LENGTH_SIN(angle_a, L_a, angle_b):
    return (math.sin(angle_b) * L_a) / math.sin(angle_a)

# Parameters
ThumbParams = [
    11.48, 0.48, 10.98, 10.00, 0.5983, 43.78, 2.08, 0.2155, 52.52, 9.90, 11.30, 1.2976, 27.54, 51.30, 26.8, 0.4184, 13.31
]

FingerParams = [
    [12.70, 8.60, 19.63, 14.25, 0.5353, 0.7624, 0.8496, 35.92, 27.44, 8.63, 7.69, 34.14, 50.23],
    [13.17, 8.60, 19.63, 14.31, 0.5353, 0.7763, 0.8496, 41.01, 32.39, 8.63, 7.69, 39.48, 50.23],
    [13.00, 8.60, 19.63, 14.25, 0.5353, 0.7624, 0.8496, 35.92, 27.44, 8.63, 7.69, 34.14, 50.23],
    [13.15, 8.60, 19.63, 14.27, 0.5353, 0.7502, 0.8141, 31.93, 23.59, 8.63, 7.53, 30.85, 35.53]
]

OffsetAngleForURDF_Thumb = [0.4243102998823798, 2.8587571556405598, 1.5319419424521146]
OffsetAngleForURDF = [
    [2.932191219049254, 2.8069436083614603, 2.5070833024292147, 2.0524510416836774],
    [2.9149674382495734, 2.7765308802396014, 2.5671093077696816, 2.050822356942187],
    [2.92349523247357, 2.7806045952072487, 2.4744162317989837, 2.0228874728582893],
    [2.91807750665145, 2.7543096279771366, 2.4606460165701547, 1.9656409021066734]
]


def THUMB_OffsetToAngle(offset_to_ref):
    pos = METER_TO_MILLIMETER(offset_to_ref)
    Angle_Thumb = [0.0] * MAX_ANGLES_URDF_THUMB
    
    # 简化的拇指角度计算
    L_BP0, L_OP, L_AB, L_OA, Angle_OAR = ThumbParams[0:5]
    L_CT0, L_OT, Angle_EOQ, L_OE, L_CD, L_ED, Angle_DEF, L_EF, XE = ThumbParams[5:14]
    
    L_BP = L_BP0 + pos
    L_OB = math.sqrt(L_OP * L_OP + L_BP * L_BP)
    Angle_OBP = math.atan(L_OP / L_BP)
    Angle_AOB = ANGLE_COS(L_AB, L_OA, L_OB)
    Angle_QOS = Angle_AOB - Angle_OBP - Angle_OAR
    
    L_CT = L_CT0 + pos
    L_OC = math.sqrt(L_CT * L_CT + L_OT * L_OT)
    Angle_OCT = math.atan(L_OT / L_CT)
    Angle_EOC = Angle_EOQ + Angle_OCT
    L_CE = LENGTH_COS(Angle_EOC, L_OE, L_OC)
    Angle_CED = ANGLE_COS(L_CD, L_CE, L_ED)
    Angle_CEF = Angle_CED + Angle_DEF
    L_CF = LENGTH_COS(Angle_CEF, L_CE, L_EF)
    Angle_ECF = ANGLE_COS(L_EF, L_CE, L_CF)
    Angle_ECU = HALF_PI - math.atan((XE - L_CT) / L_CE)
    Angle_DCT = ANGLE_COS(L_ED, L_CD, L_CE) + (HALF_PI - Angle_ECU + Angle_ECF) + HALF_PI
    
    L_OD = LENGTH_COS(Angle_DCT - Angle_OCT, L_OC, L_CD)
    Angle_OED = ANGLE_COS(L_OD, L_OE, L_ED)
    
    Angle_Thumb[0] = Angle_QOS - OffsetAngleForURDF_Thumb[0]
    Angle_Thumb[1] = Angle_DCT - OffsetAngleForURDF_Thumb[1]
    Angle_Thumb[2] = OffsetAngleForURDF_Thumb[2] - Angle_OED
    
    return Angle_Thumb


def FINGER_OffsetToAngle(finger_id, offset_to_ref):
    pos = METER_TO_MILLIMETER(offset_to_ref)
    finger_id -= 1
    Angle_Finger = [[0] * MAX_ANGLES_URDF for _ in range(MAX_ANGLES_URDF)]
    
    # 简化的手指角度计算
    params = FingerParams[finger_id]
    X_A0, L_AP, L_AC, L_OC, Angle_BOQ, Angle_COD, Angle_EDF = params[0:7]
    L_OD, L_OB, L_DE, L_BE = params[7], params[9], params[10], params[11]
    
    L_OP = X_A0 + pos
    L_OA = math.sqrt(L_OP * L_OP + L_AP * L_AP)
    Angle_AOP = ANGLE_COS(L_AP, L_OA, L_OP)
    Angle_OAP = HALF_PI - Angle_AOP
    Angle_CAO = ANGLE_COS(L_OC, L_AC, L_OA)
    Angle_CAR = ONE_HALF_PI - Angle_OAP - Angle_CAO
    
    Angle_COA = ANGLE_COS(L_AC, L_OC, L_OA)
    Angle_COP = Angle_AOP + Angle_COA
    Angle_DOP = Angle_COD + Angle_COP
    
    Angle_DOQ = PI - Angle_DOP
    Angle_DOB = Angle_BOQ + Angle_DOQ
    L_BD = LENGTH_COS(Angle_DOB, L_OB, L_OD)
    Angle_BDO = ANGLE_COS(L_OB, L_BD, L_OD)
    Angle_BDE = ANGLE_COS(L_BE, L_DE, L_BD)
    Angle_EDO = Angle_BDE - Angle_BDO
    L_OE = LENGTH_COS(Angle_BDE, L_DE, L_OD)
    Angle_EBO = ANGLE_COS(L_OE, L_BE, L_OB)
    
    Angle_FDO = Angle_EDF + Angle_EDO
    
    Angle_Finger[finger_id][0] = Angle_CAR - OffsetAngleForURDF[finger_id][0]
    Angle_Finger[finger_id][1] = Angle_DOP - OffsetAngleForURDF[finger_id][1]
    Angle_Finger[finger_id][2] = Angle_FDO - OffsetAngleForURDF[finger_id][2]
    Angle_Finger[finger_id][3] = Angle_EBO - OffsetAngleForURDF[finger_id][3]
    
    return Angle_Finger[finger_id]


def HAND_FingerPosToAngle(finger_id, pos):
    angles = [0] * MAX_ANGLES_URDF
    
    if finger_id >= NUM_FINGERS + EXTRA_MOTORS:
        return None
    elif finger_id >= NUM_FINGERS:
        return pos
    else:
        if finger_id == 0:
            return THUMB_OffsetToAngle(pos)
        else:
            return FINGER_OffsetToAngle(finger_id, pos)


class HandJointLinkage(BehaviorScript):
    
    def on_init(self):
        try:
            self.debug_flag = True
            self._timer_task = None
            self._is_running = False
            self._update_counter = 0
        except Exception as e:
            print(f"HandJointLinkage on_init error: {e}")
    
    def on_destroy(self):
        try:
            self._stop_joint_linkage_timer()
        except Exception as e:
            print(f"HandJointLinkage on_destroy error: {e}")
    
    def on_play(self):
        try:
            self._start_joint_linkage_timer()
        except Exception as e:
            print(f"HandJointLinkage on_play error: {e}")
    
    def on_pause(self):
        try:
            self._stop_joint_linkage_timer()
        except Exception as e:
            print(f"HandJointLinkage on_pause error: {e}")
    
    def on_stop(self):
        try:
            self._stop_joint_linkage_timer()
        except Exception as e:
            print(f"HandJointLinkage on_stop error: {e}")
    
    def _start_joint_linkage_timer(self):
        """启动手部关节联动定时器"""
        try:
            if not self._is_running:
                self._is_running = True
                self._update_counter = 0
                self._timer_task = asyncio.ensure_future(self._joint_linkage_loop())
        except Exception as e:
            print(f"HandJointLinkage _start_joint_linkage_timer error: {e}")
    
    def _stop_joint_linkage_timer(self):
        """停止手部关节联动定时器"""
        try:
            self._is_running = False
            if self._timer_task:
                self._timer_task.cancel()
                self._timer_task = None
        except Exception as e:
            print(f"HandJointLinkage _stop_joint_linkage_timer error: {e}")
    
    async def _joint_linkage_loop(self):
        """手部关节联动主循环"""
        try:
            import omni.kit.app
            self._update_frequency_hz = 30
            self._frame_skip = max(1, int(60 / self._update_frequency_hz))
            
            while self._is_running:
                self._update_counter += 1
                
                if self._update_counter % self._frame_skip == 0:
                    self._update_hand_joint_linkage()
                
                await omni.kit.app.get_app().next_update_async()
                
        except asyncio.CancelledError:
            pass
        except Exception as e:
            print(f"HandJointLinkage _joint_linkage_loop error: {e}")
    
    def find_joint_path_by_name(self, joint_name):
        """通过关节名统一查找该关节的路径"""
        try:
            stage = omni.usd.get_context().get_stage()
            if not stage or not hasattr(self, 'prim') or not self.prim:
                return None
                
            joints_scope_path = self.prim.GetPath()
            
            # 直接查找
            direct_joint_path = joints_scope_path.AppendChild(joint_name)
            direct_prim = stage.GetPrimAtPath(direct_joint_path)
            if direct_prim.IsValid():
                return str(direct_joint_path)
            
            # 递归查找
            joints_scope_prim = stage.GetPrimAtPath(joints_scope_path)
            if joints_scope_prim.IsValid():
                found_path = self._recursive_find_joint(stage, joints_scope_prim, joint_name)
                if found_path:
                    return found_path
            
            return None
            
        except Exception as e:
            if self.debug_flag:
                print(f"HandJointLinkage find_joint_path_by_name error: {e}")
            return None
    
    def _recursive_find_joint(self, stage, parent_prim, joint_name, max_depth=3, current_depth=0):
        """递归查找关节"""
        try:
            if current_depth > max_depth:
                return None
                
            for child_prim in parent_prim.GetChildren():
                if child_prim.GetName() == joint_name:
                    return str(child_prim.GetPath())
                
                if current_depth < max_depth:
                    found_path = self._recursive_find_joint(stage, child_prim, joint_name, max_depth, current_depth + 1)
                    if found_path:
                        return found_path
            
            return None
            
        except Exception as e:
            return None

    def get_all_joint_paths(self):
        """获取所有已知关节的路径映射"""
        try:
            joint_paths = {}
            for finger_id in range(len(JOINTS_NAME)):
                joint_names = JOINTS_NAME[finger_id]
                for joint_name in joint_names:
                    joint_path = self.find_joint_path_by_name(joint_name)
                    if joint_path:
                        joint_paths[joint_name] = joint_path
            return joint_paths
        except Exception as e:
            return {}

    def _update_hand_joint_linkage(self):
        """执行手部关节联动计算"""
        try:
            stage = omni.usd.get_context().get_stage()
            if not stage or not hasattr(self, 'prim') or not self.prim:
                return

            for finger_id in range(NUM_FINGERS):
                try:
                    joint_names = JOINTS_NAME[finger_id]

                    # 确定slider关节
                    if finger_id == THUMB_ID:
                        slider_joint_name = joint_names[1]  # th_slider_link
                        slider_joint_index = 1
                    else:
                        slider_joint_name = joint_names[0]  # *f_slider_link
                        slider_joint_index = 0

                    # 查找slider关节
                    slider_joint_path_str = self.find_joint_path_by_name(slider_joint_name)
                    if not slider_joint_path_str:
                        continue

                    slider_joint_path = Sdf.Path(slider_joint_path_str)
                    slider_prim = stage.GetPrimAtPath(slider_joint_path)

                    # 获取slider关节的当前位移
                    current_position = self._get_joint_position(slider_prim, is_prismatic=True)
                    if current_position is None:
                        continue

                    # 限制位移范围
                    if finger_id == THUMB_ID:
                        clamped_position = CLAMP(current_position, -0.003, 0.008)
                    else:
                        clamped_position = CLAMP(current_position, -0.003, 0.016)

                    # 计算目标角度
                    target_angles = HAND_FingerPosToAngle(finger_id, clamped_position)
                    if not target_angles:
                        continue

                    # 设置其他关节到目标角度
                    for joint_idx, joint_name in enumerate(joint_names):
                        if joint_idx == slider_joint_index:
                            continue

                        joint_path_str = self.find_joint_path_by_name(joint_name)
                        if not joint_path_str:
                            continue

                        joint_path = Sdf.Path(joint_path_str)
                        joint_prim = stage.GetPrimAtPath(joint_path)

                        # 计算目标角度索引
                        angle_index = self._get_angle_index_for_joint(finger_id, joint_idx, slider_joint_index)

                        if angle_index >= 0 and isinstance(target_angles, list) and angle_index < len(target_angles):
                            target_angle = target_angles[angle_index]
                            self._set_joint_position(joint_prim, target_angle, is_prismatic=False)

                except Exception as e:
                    if self.debug_flag:
                        print(f"HandJointLinkage: Error processing finger {finger_id}: {e}")

        except Exception as e:
            if self.debug_flag:
                print(f"HandJointLinkage _update_hand_joint_linkage error: {e}")

    def _get_joint_position(self, joint_prim, is_prismatic=True):
        """获取关节的当前位置"""
        try:
            # 尝试从drive target获取
            attr_name = "drive:linear:physics:targetPosition" if is_prismatic else "drive:angular:physics:targetPosition"
            drive_attr = joint_prim.GetAttribute(attr_name)
            if drive_attr and drive_attr.HasValue():
                return drive_attr.Get()

            # 备用方法
            pos_attr = joint_prim.GetAttribute("physics:position")
            if pos_attr and pos_attr.HasValue():
                return pos_attr.Get()

            return 0.005 if is_prismatic else 0.0

        except Exception as e:
            return None

    def _set_joint_position(self, joint_prim, target_value, is_prismatic=True):
        """设置关节的目标位置"""
        try:
            target_value_to_set = math.degrees(target_value) if not is_prismatic else target_value

            # 设置drive target
            attr_name = "drive:linear:physics:targetPosition" if is_prismatic else "drive:angular:physics:targetPosition"
            drive_attr = joint_prim.GetAttribute(attr_name)
            if not drive_attr:
                drive_attr = joint_prim.CreateAttribute(attr_name, Sdf.ValueTypeNames.Float)

            if drive_attr:
                drive_attr.Set(float(target_value_to_set))

        except Exception as e:
            if self.debug_flag:
                print(f"HandJointLinkage _set_joint_position error: {e}")

    def _get_angle_index_for_joint(self, finger_id, joint_idx, slider_joint_index):
        """获取关节对应的角度索引"""
        try:
            if finger_id == THUMB_ID:
                joint_to_angle_map = {0: 0, 1: -1, 2: 1, 3: 2}
                return joint_to_angle_map.get(joint_idx, -1)
            else:
                joint_to_angle_map = {0: -1, 1: 0, 2: 1, 3: 2, 4: 3}
                return joint_to_angle_map.get(joint_idx, -1)
        except Exception as e:
            return -1
